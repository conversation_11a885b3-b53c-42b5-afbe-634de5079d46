apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: tiptop
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        # Optional: Add persistence
        volumeMounts:
        - name: redis-data
          mountPath: /data
        # Redis configuration for pub/sub optimization
        command: ["redis-server"]
        args: ["--appendonly", "yes", "--maxmemory", "100mb", "--maxmemory-policy", "allkeys-lru"]
      volumes:
      - name: redis-data
        emptyDir: {}
      # For production, consider using PersistentVolume:
      # - name: redis-data
      #   persistentVolumeClaim:
      #     claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: tiptop
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP
  type: ClusterIP
