 TipTop Content Script Loaded.
 AI cache cleared on extension reload
 TipTop Content Script Initialized.
 🚀 Early initializing TipTop social client for background communication
 🚀 Initializing TipTop social client
 TipTop social features enabled: true
 TipTop user name: Insightful Darwin
 Found mode indicator file: Object
 Mock data DISABLED from indicator file
 Mode set to PRODUCTION from indicator file
 TipTop configuration updated: Object
 Loaded existing TipTop user ID from sync storage: user_1752016153627_d3gty0wao
 Social client initialized with userId: user_1752016153627_d3gty0wao
 Social client initialized with userName: Insightful Darwin
 Social features enabled: true
 Connecting through background service worker
 Background WebSocket connection initialized, connected: true
 🔗 Connection state updated: Object
 Message history requested: Object
 Setting up background message listener
 📱 UI not ready yet, will load messages when UI is available
 Requesting initial user list after social client initialization
 ✅ Social client initialization complete
 ✅ TipTop social client initialization complete
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 TipTop button created.
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 Button interaction: Object
 Detected as click - calling handleButtonClick
 TipTop button clicked - calling toggleTipTopPanel
 TipTop panel created.
 Adding event listeners to chat send buttons
 Adding event listeners to chat inputs for Enter key
 🚀 Initializing TipTop social client
 ⚠️ Social client already initialized, skipping
 Toggling social features: ON
 Social features toggled. Current state: Object
 🔗 Background WebSocket already connected, preserving connection state
 📱 UI is now ready, loading existing messages
 Loading existing shared messages from chrome.storage.local
 Adding event listeners to chat send buttons
 Adding event listeners to chat inputs for Enter key
 🚀 Initializing TipTop social client
 ⚠️ Social client already initialized, skipping
 Toggling social features: ON
 Social features toggled. Current state: Object
 🔗 Background WebSocket already connected, preserving connection state
 📱 UI is now ready, loading existing messages
 Loading existing shared messages from chrome.storage.local
 Restoring social features after reopening panel
 Using dedicated panel reopen handler
 🚀 Initializing TipTop social client
 ⚠️ Social client already initialized, skipping
 Loaded 0 existing messages for current URL
 Requesting user list after UI became ready
 Loaded 0 existing messages for current URL
 Requesting user list after UI became ready
 🔄 Handling panel reopen
 Ensuring chat UI is properly initialized
 Chat log element not found, may need to initialize social UI
 WebSocket already connected, refreshing UI
 Requesting users list after panel reopen
 Requesting history messages after panel reopen with higher limit
 Connecting through background service worker
 Connecting through background service worker
 Background WebSocket connection initialized, connected: true
 🔗 Connection state updated: Object
 Connecting through background service worker
 Connecting through background service worker
 All AI content hidden - toggle is off
 Message history requested: Object
 Scheduled 5 retry attempts for user list updates
 Background WebSocket connection initialized, connected: true
 🔗 Connection state updated: Object
 Message history requested: Object
 Clearing existing retry timeouts: 5
 Scheduled 5 retry attempts for user list updates
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Reinitializing chat container after navigation
 Recreated chat log element after navigation
 Set up enhanced MutationObserver for chat log
 Added message ID to processed set: msg_1752016226872_6xz5k83be
 📝 Added new message to chat log, triggering scroll
 Force scrolling because chat log is newly created or panel was just reopened
 MutationObserver detected 1 new message(s) added
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752016231472_k53n9x2qz
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752016237663_0aqsy1mcb
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752016240568_xaxa4wr80
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔄 Force scrolling chat on panel reopen
 ⏱️ 0ms: Direct scroll attempt - scrollTop=0, scrollHeight=327
 ⏱️ 0ms: scrollIntoView on last message
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 ⏱️ 50ms: Direct scroll attempt - scrollTop=0, scrollHeight=327
 ⏱️ 50ms: scrollIntoView on last message
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 Loading existing shared messages from chrome.storage.local
 Loaded 0 existing messages for current URL
 ⏱️ 100ms: Direct scroll attempt - scrollTop=0, scrollHeight=327
 ⏱️ 100ms: scrollIntoView on last message
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 ⏱️ 200ms: Direct scroll attempt - scrollTop=0, scrollHeight=327
 ⏱️ 200ms: scrollIntoView on last message
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2847 ⏱️ 500ms: Direct scroll attempt - scrollTop=0, scrollHeight=327
social-client.js:2854 ⏱️ 500ms: scrollIntoView on last message
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2161 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 327, scrollTop: 0
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2171 ✅ Final scroll attempt completed
social-client.js:2182 ✅ Scroll successful - at bottom
social-client.js:2400 Retry 1000ms: Skipped - already have users or not connected Object
social-client.js:2847 ⏱️ 1000ms: Direct scroll attempt - scrollTop=0, scrollHeight=327
social-client.js:2854 ⏱️ 1000ms: scrollIntoView on last message
social-client.js:2400 Retry 3000ms: Skipped - already have users or not connected Object
social-client.js:2400 Retry 5000ms: Skipped - already have users or not connected {isConnected: true, activeUsersCount: 2, socialEnabled: true}
