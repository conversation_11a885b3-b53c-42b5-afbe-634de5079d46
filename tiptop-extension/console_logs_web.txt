 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Reinitializing chat container after navigation
 Recreated chat log element after navigation
 Set up enhanced MutationObserver for chat log
 Added message ID to processed set: msg_1752076264435_i9xdz622p
 📝 Added new message to chat log, triggering scroll
 Force scrolling because chat log is newly created or panel was just reopened
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076264435_i9xdz622p
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 Enter key pressed in chat input
 Send chat message function called
 Message content: h2
 Calling TipTopSocial.sendChatMessage
 sendChatMessage called with message: h2
 🚀 sendChatMessageThroughBackground called with content: h2
 📤 Sending message to background: Object
 🔍 Runtime available: true
 🔍 Extension context valid: true
 Send result: Promise
 Enter key pressed in chat input
 Send chat message function called
 Message content: 
 Message is empty, not sending
 📥 Background response received: Object
 🔍 Response timestamp: 2025-07-09T15:51:08.036Z
 ✅ Chat message sent successfully through background
 Chat message sent successfully through background
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752076268033_j3f3k77y8
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076268033_j3f3k77y8
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 Received TIPTOP_RESET_UI message, resetting social state Object
 🔄 Resetting social state for navigation (UI only) Object
 Cleared chat log UI for navigation (messages persist for other users)
 Cleared history content UI for navigation
 Cleared processed message IDs
 Reset active users and message queue
 Cleared user list UI
 Navigation reset complete for URL: https://en.wikipedia.org/wiki/Money
 🔥 Content script received message from background: TIPTOP_RESET_UI Object
 🔄 Social client received navigation reset: Object
 🔄 Handling navigation reset in social client: Object
 Cleared chat log for navigation
 🔄 Requesting fresh data for new URL: https://en.wikipedia.org/wiki/Money
 Message history requested: Object
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 Reconnecting social client for new page
 Connecting through background service worker
 Background WebSocket connection initialized, connected: true
 🔗 Connection state updated: Object
 Message history requested: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 Enter key pressed in chat input
 Send chat message function called
 Message content: t1
 Calling TipTopSocial.sendChatMessage
 sendChatMessage called with message: t1
 🚀 sendChatMessageThroughBackground called with content: t1
 📤 Sending message to background: Object
 🔍 Runtime available: true
 🔍 Extension context valid: true
 Send result: Promise
 Enter key pressed in chat input
 Send chat message function called
 Message content: 
 Message is empty, not sending
 📥 Background response received: Object
 🔍 Response timestamp: 2025-07-09T15:52:39.796Z
 ✅ Chat message sent successfully through background
 Chat message sent successfully through background
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752076359794_jlffddv3m
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 150, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 2 users: Array(2)
 Updating active users list from server: Array(2)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752076368634_2tx9ykeu5
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 157, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 Enter key pressed in chat input
 Send chat message function called
 Message content: t3
 Calling TipTopSocial.sendChatMessage
 sendChatMessage called with message: t3
 🚀 sendChatMessageThroughBackground called with content: t3
 📤 Sending message to background: Object
 🔍 Runtime available: true
 🔍 Extension context valid: true
 Send result: Promise
 Enter key pressed in chat input
 Send chat message function called
 Message content: 
 Message is empty, not sending
 📥 Background response received: undefined
 🔍 Response timestamp: undefined
 ❌ Failed to send chat message through background
sendChatMessageThroughBackground @ chrome-extension://h…ocial-client.js:337
 ❌ Response details: undefined
sendChatMessageThroughBackground @ chrome-extension://h…ocial-client.js:338
 ⚠️ Runtime messaging failed, trying storage communication: 
sendChatMessageThroughBackground @ chrome-extension://h…ocial-client.js:342
 🔄 Using storage-based communication for message: Object
 📤 Message request stored in chrome.storage: tiptop_send_request_msg_1752076519589_9pxbqcuct
 📥 Storage response received: Object
 Failed to send chat message through background
sendChatMessage @ chrome-extension://h…cial-client.js:1422
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752076264435_i9xdz622p
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752076268033_j3f3k77y8
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076359794_jlffddv3m
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 🔄 Skipping duplicate message: msg_1752076368634_2tx9ykeu5
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔥 Content script received message from background: TIPTOP_CHAT_MESSAGE Object
 🔥 Handling incoming message: chat Object
 Added message ID to processed set: msg_1752076519589_9pxbqcuct
 📝 Added new message to chat log, triggering scroll
 MutationObserver detected 1 new message(s) added
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔥 Content script received message from background: TIPTOP_HISTORY_LOADED Object
 History loaded: Object
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_SCROLL_CHAT Object
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempting to scroll chat to bottom
 🔍 Chat log element exists: true
 🔍 Chat container exists: true
 🔍 SCROLL DEBUG: Current scroll state: Object
 🔍 SCROLL DEBUG: Executing aggressive scroll sequence
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 1 (10ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 2 (50ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 3 (100ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔥 Content script received message from background: TIPTOP_USERS_UPDATE Object
 🔥 Handling users update: Object
 📋 Received 1 users: Array(1)
 Updating active users list from server: Array(1)
 User list update: Object
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 4 (300ms) - scrollHeight: 411, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔍 SCROLL DEBUG: Attempt 5 (500ms) - scrollHeight: 411, scrollTop: 0
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 ✅ Final scroll attempt completed
 ✅ Scroll successful - at bottom
 🔥 Content script received message from background: TIPTOP_TEST_MESSAGE Object
 🧪 Received test message from background: Object
