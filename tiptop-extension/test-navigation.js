// Test script to verify navigation and chat history clearing
console.log('🧪 TipTop Navigation Test Script Loaded');

// Function to test chat history clearing
function testChatHistoryClearing() {
  console.log('🧪 Testing chat history clearing...');
  
  // Check if chat log exists and has content
  const chatLog = document.getElementById('tiptop-chat-log');
  if (chatLog) {
    console.log('🧪 Chat log found, current message count:', chatLog.children.length);
    
    // Simulate navigation by calling the reset function
    if (window.resetSocialStateForNavigation) {
      console.log('🧪 Calling resetSocialStateForNavigation...');
      window.resetSocialStateForNavigation({ 
        oldUrl: 'https://example.com/old', 
        newUrl: window.location.href 
      });
      
      // Check if chat log was cleared
      setTimeout(() => {
        console.log('🧪 After reset - Chat log message count:', chatLog.children.length);
        if (chatLog.children.length === 0) {
          console.log('✅ Chat history clearing test PASSED');
        } else {
          console.log('❌ Chat history clearing test FAILED');
        }
      }, 100);
    } else {
      console.log('❌ resetSocialStateForNavigation function not found');
    }
  } else {
    console.log('❌ Chat log element not found');
  }
}

// Function to test WebSocket connection on panel open
function testWebSocketConnection() {
  console.log('🧪 Testing WebSocket connection on panel open...');
  
  // Check if TipTop social client exists
  if (window.TipTopSocial) {
    console.log('🧪 TipTop social client found');
    
    // Check connection status
    if (window.TipTopSocial.isWebSocketConnected && window.TipTopSocial.isWebSocketConnected()) {
      console.log('✅ WebSocket connection test PASSED - Already connected');
    } else {
      console.log('🧪 WebSocket not connected, testing loadMessagesWhenReady...');
      
      // Test the loadMessagesWhenReady function
      if (window.TipTopSocial.loadMessagesWhenReady) {
        window.TipTopSocial.loadMessagesWhenReady().then(() => {
          console.log('🧪 loadMessagesWhenReady completed');
          
          // Check connection again
          if (window.TipTopSocial.isWebSocketConnected && window.TipTopSocial.isWebSocketConnected()) {
            console.log('✅ WebSocket connection test PASSED - Connected after loadMessagesWhenReady');
          } else {
            console.log('❌ WebSocket connection test FAILED - Still not connected');
          }
        }).catch(error => {
          console.log('❌ WebSocket connection test FAILED - Error:', error);
        });
      } else {
        console.log('❌ loadMessagesWhenReady function not found');
      }
    }
  } else {
    console.log('❌ TipTop social client not found');
  }
}

// Function to test storage clearing
async function testStorageClearing() {
  console.log('🧪 Testing storage clearing...');
  
  try {
    // Get current storage state
    const beforeStorage = await chrome.storage.local.get(null);
    const beforeMessageCount = Object.keys(beforeStorage).filter(key => key.startsWith('tiptop_msg_')).length;
    console.log('🧪 Messages in storage before clearing:', beforeMessageCount);
    
    // Add a test message to storage
    const testMessage = {
      messageId: 'test_msg_' + Date.now(),
      url: window.location.href,
      content: 'Test message for clearing',
      timestamp: new Date().toISOString(),
      userId: 'test_user',
      userName: 'Test User',
      type: 'chat'
    };
    
    await chrome.storage.local.set({
      [`tiptop_msg_${testMessage.messageId}`]: testMessage
    });
    
    console.log('🧪 Added test message to storage');
    
    // Simulate navigation clearing by sending message to background
    chrome.runtime.sendMessage({
      type: 'TIPTOP_TEST_CLEAR_STORAGE',
      url: window.location.href
    });
    
    // Check storage after a delay
    setTimeout(async () => {
      const afterStorage = await chrome.storage.local.get(null);
      const afterMessageCount = Object.keys(afterStorage).filter(key => key.startsWith('tiptop_msg_')).length;
      console.log('🧪 Messages in storage after clearing:', afterMessageCount);
      
      if (afterMessageCount < beforeMessageCount + 1) {
        console.log('✅ Storage clearing test PASSED');
      } else {
        console.log('❌ Storage clearing test FAILED');
      }
    }, 1000);
    
  } catch (error) {
    console.log('❌ Storage clearing test FAILED - Error:', error);
  }
}

// Run tests when TipTop panel is opened
function runTests() {
  console.log('🧪 Running TipTop navigation tests...');
  
  testChatHistoryClearing();
  testWebSocketConnection();
  testStorageClearing();
}

// Export test functions for manual testing
window.TipTopNavigationTests = {
  runTests,
  testChatHistoryClearing,
  testWebSocketConnection,
  testStorageClearing
};

console.log('🧪 TipTop Navigation Test Script Ready - Use TipTopNavigationTests.runTests() to run all tests');
