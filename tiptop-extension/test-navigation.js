// Test script to verify navigation and chat history clearing
console.log('🧪 TipTop Navigation Test Script Loaded');

// Function to test navigation behavior
function testNavigationBehavior() {
  console.log('🧪 Testing navigation behavior...');

  // Check if chat log exists
  const chatLog = document.getElementById('tiptop-chat-log');
  if (chatLog) {
    console.log('🧪 Chat log found, current message count:', chatLog.children.length);

    // Add some test messages to simulate existing chat
    if (chatLog.children.length === 0) {
      chatLog.innerHTML = `
        <div class="chat-message">Test message 1</div>
        <div class="chat-message">Test message 2</div>
      `;
      console.log('🧪 Added test messages to chat log');
    }

    // Simulate navigation by sending reset message
    console.log('🧪 Simulating navigation reset...');
    chrome.runtime.sendMessage({
      type: 'TIPTOP_RESET_UI',
      data: {
        oldUrl: 'https://example.com/old',
        newUrl: window.location.href
      }
    });

    // Check if chat log was cleared after a delay
    setTimeout(() => {
      console.log('🧪 After navigation - Chat log message count:', chatLog.children.length);
      if (chatLog.children.length === 0) {
        console.log('✅ Navigation UI clearing test PASSED');
      } else {
        console.log('❌ Navigation UI clearing test FAILED - UI not cleared');
      }
    }, 500);
  } else {
    console.log('❌ Chat log element not found');
  }
}

// Function to test WebSocket connection on panel open
function testWebSocketConnection() {
  console.log('🧪 Testing WebSocket connection on panel open...');
  
  // Check if TipTop social client exists
  if (window.TipTopSocial) {
    console.log('🧪 TipTop social client found');
    
    // Check connection status
    if (window.TipTopSocial.isWebSocketConnected && window.TipTopSocial.isWebSocketConnected()) {
      console.log('✅ WebSocket connection test PASSED - Already connected');
    } else {
      console.log('🧪 WebSocket not connected, testing loadMessagesWhenReady...');
      
      // Test the loadMessagesWhenReady function
      if (window.TipTopSocial.loadMessagesWhenReady) {
        window.TipTopSocial.loadMessagesWhenReady().then(() => {
          console.log('🧪 loadMessagesWhenReady completed');
          
          // Check connection again
          if (window.TipTopSocial.isWebSocketConnected && window.TipTopSocial.isWebSocketConnected()) {
            console.log('✅ WebSocket connection test PASSED - Connected after loadMessagesWhenReady');
          } else {
            console.log('❌ WebSocket connection test FAILED - Still not connected');
          }
        }).catch(error => {
          console.log('❌ WebSocket connection test FAILED - Error:', error);
        });
      } else {
        console.log('❌ loadMessagesWhenReady function not found');
      }
    }
  } else {
    console.log('❌ TipTop social client not found');
  }
}

// Function to test storage persistence (messages should NOT be cleared)
async function testStoragePersistence() {
  console.log('🧪 Testing storage persistence...');

  try {
    // Get current storage state
    const beforeStorage = await chrome.storage.local.get(null);
    const beforeMessageCount = Object.keys(beforeStorage).filter(key => key.startsWith('tiptop_msg_')).length;
    console.log('🧪 Messages in storage before navigation:', beforeMessageCount);

    // Add a test message to storage
    const testMessage = {
      messageId: 'test_msg_' + Date.now(),
      url: window.location.href,
      content: 'Test message for persistence',
      timestamp: new Date().toISOString(),
      userId: 'test_user',
      userName: 'Test User',
      type: 'chat'
    };

    await chrome.storage.local.set({
      [`tiptop_msg_${testMessage.messageId}`]: testMessage
    });

    console.log('🧪 Added test message to storage');

    // Simulate navigation (should NOT clear storage)
    if (window.resetSocialStateForNavigation) {
      window.resetSocialStateForNavigation({
        oldUrl: window.location.href,
        newUrl: 'https://example.com/new-page'
      });
    }

    // Check storage after a delay - messages should still be there
    setTimeout(async () => {
      const afterStorage = await chrome.storage.local.get(null);
      const afterMessageCount = Object.keys(afterStorage).filter(key => key.startsWith('tiptop_msg_')).length;
      console.log('🧪 Messages in storage after navigation:', afterMessageCount);

      if (afterMessageCount >= beforeMessageCount + 1) {
        console.log('✅ Storage persistence test PASSED - Messages preserved');
      } else {
        console.log('❌ Storage persistence test FAILED - Messages were cleared');
      }
    }, 1000);

  } catch (error) {
    console.log('❌ Storage persistence test FAILED - Error:', error);
  }
}

// Run tests when TipTop panel is opened
function runTests() {
  console.log('🧪 Running TipTop navigation tests...');

  testNavigationBehavior();
  testWebSocketConnection();
  testStoragePersistence();
}

// Export test functions for manual testing
window.TipTopNavigationTests = {
  runTests,
  testNavigationBehavior,
  testWebSocketConnection,
  testStoragePersistence
};

console.log('🧪 TipTop Navigation Test Script Ready - Use TipTopNavigationTests.runTests() to run all tests');
