<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TipTop Extension Messaging Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .log-area {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>TipTop Extension Messaging Test</h1>
    
    <div class="test-section">
        <h2>Extension Status</h2>
        <div id="extension-status" class="status info">Checking extension status...</div>
        <button class="test-button" onclick="checkExtensionStatus()">Refresh Status</button>
    </div>

    <div class="test-section">
        <h2>WebSocket Connection Test</h2>
        <div id="websocket-status" class="status info">Not tested</div>
        <button class="test-button" onclick="testWebSocketConnection()">Test WebSocket</button>
    </div>

    <div class="test-section">
        <h2>Chat Message Test</h2>
        <input type="text" id="test-message" placeholder="Enter test message" style="width: 300px; padding: 8px;">
        <button class="test-button" onclick="sendTestMessage()">Send Test Message</button>
        <div id="message-status" class="status info">Ready to send test message</div>
    </div>

    <div class="test-section">
        <h2>Storage Communication Test</h2>
        <button class="test-button" onclick="testStorageCommunication()">Test Storage Fallback</button>
        <div id="storage-status" class="status info">Ready to test storage communication</div>
    </div>

    <div class="test-section">
        <h2>Debug Log</h2>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
        <div id="debug-log" class="log-area"></div>
    </div>

    <script>
        let logArea = document.getElementById('debug-log');
        
        function log(message) {
            const timestamp = new Date().toISOString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logArea.textContent = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function checkExtensionStatus() {
            log('Checking extension status...');
            
            try {
                // Check if TipTop extension is loaded
                if (typeof window.TipTopSocial !== 'undefined') {
                    updateStatus('extension-status', 'TipTop extension is loaded and social client is available', 'success');
                    log('✅ TipTop extension detected');
                    
                    // Check connection status
                    if (window.TipTopSocial.isConnected && window.TipTopSocial.isConnected()) {
                        log('✅ WebSocket connection is active');
                    } else {
                        log('⚠️ WebSocket connection not active');
                    }
                } else {
                    updateStatus('extension-status', 'TipTop extension not detected. Please make sure it is installed and enabled.', 'error');
                    log('❌ TipTop extension not detected');
                }
            } catch (error) {
                updateStatus('extension-status', `Error checking extension: ${error.message}`, 'error');
                log(`❌ Error checking extension: ${error.message}`);
            }
        }

        async function testWebSocketConnection() {
            log('Testing WebSocket connection...');
            
            try {
                if (typeof window.TipTopSocial === 'undefined') {
                    updateStatus('websocket-status', 'TipTop extension not available', 'error');
                    return;
                }

                // Initialize social client if not already done
                if (window.TipTopSocial.initialize) {
                    log('Initializing social client...');
                    await window.TipTopSocial.initialize();
                }

                // Check connection status
                if (window.TipTopSocial.isConnected && window.TipTopSocial.isConnected()) {
                    updateStatus('websocket-status', 'WebSocket connection is active', 'success');
                    log('✅ WebSocket connection test passed');
                } else {
                    updateStatus('websocket-status', 'WebSocket connection is not active', 'error');
                    log('❌ WebSocket connection test failed');
                }
            } catch (error) {
                updateStatus('websocket-status', `WebSocket test error: ${error.message}`, 'error');
                log(`❌ WebSocket test error: ${error.message}`);
            }
        }

        async function sendTestMessage() {
            const messageInput = document.getElementById('test-message');
            const message = messageInput.value.trim();
            
            if (!message) {
                updateStatus('message-status', 'Please enter a test message', 'error');
                return;
            }

            log(`Sending test message: "${message}"`);
            
            try {
                if (typeof window.TipTopSocial === 'undefined') {
                    updateStatus('message-status', 'TipTop extension not available', 'error');
                    return;
                }

                if (window.TipTopSocial.sendChatMessage) {
                    const success = await window.TipTopSocial.sendChatMessage(message);
                    
                    if (success) {
                        updateStatus('message-status', 'Test message sent successfully!', 'success');
                        log('✅ Test message sent successfully');
                        messageInput.value = '';
                    } else {
                        updateStatus('message-status', 'Failed to send test message', 'error');
                        log('❌ Failed to send test message');
                    }
                } else {
                    updateStatus('message-status', 'sendChatMessage function not available', 'error');
                    log('❌ sendChatMessage function not available');
                }
            } catch (error) {
                updateStatus('message-status', `Error sending message: ${error.message}`, 'error');
                log(`❌ Error sending message: ${error.message}`);
            }
        }

        async function testStorageCommunication() {
            log('Testing storage-based communication...');
            
            try {
                // Test storage write/read
                const testKey = 'tiptop_test_storage';
                const testData = { test: true, timestamp: Date.now() };
                
                await chrome.storage.local.set({ [testKey]: testData });
                log('✅ Storage write test passed');
                
                const result = await chrome.storage.local.get(testKey);
                if (result[testKey] && result[testKey].test) {
                    updateStatus('storage-status', 'Storage communication test passed', 'success');
                    log('✅ Storage read test passed');
                    
                    // Clean up
                    await chrome.storage.local.remove(testKey);
                    log('✅ Storage cleanup completed');
                } else {
                    updateStatus('storage-status', 'Storage read test failed', 'error');
                    log('❌ Storage read test failed');
                }
            } catch (error) {
                updateStatus('storage-status', `Storage test error: ${error.message}`, 'error');
                log(`❌ Storage test error: ${error.message}`);
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Test page loaded');
            checkExtensionStatus();
        });

        // Listen for TipTop messages to verify communication
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.type && message.type.startsWith('TIPTOP_')) {
                    log(`📥 Received TipTop message: ${message.type}`);
                }
            });
        }
    </script>
</body>
</html>
