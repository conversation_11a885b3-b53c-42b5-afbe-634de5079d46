<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .log-area {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    
    <div class="test-section">
        <h2>Production WebSocket Server</h2>
        <div id="prod-status" class="status info">Not tested</div>
        <button class="test-button" onclick="testProductionWebSocket()">Test wss://ws.tiptop.qubitrhythm.com</button>
    </div>

    <div class="test-section">
        <h2>Local WebSocket Server</h2>
        <div id="local-status" class="status info">Not tested</div>
        <button class="test-button" onclick="testLocalWebSocket()">Test ws://localhost:8080</button>
    </div>

    <div class="test-section">
        <h2>Send Test Message</h2>
        <input type="text" id="test-message" placeholder="Enter test message" style="width: 300px; padding: 8px;">
        <button class="test-button" onclick="sendTestMessage()">Send Message</button>
        <div id="message-status" class="status info">Connect to WebSocket first</div>
    </div>

    <div class="test-section">
        <h2>Connection Log</h2>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
        <div id="debug-log" class="log-area"></div>
    </div>

    <script>
        let currentSocket = null;
        let logArea = document.getElementById('debug-log');
        
        function log(message) {
            const timestamp = new Date().toISOString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logArea.textContent = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function testWebSocketConnection(url, statusElementId) {
            log(`Testing WebSocket connection to: ${url}`);
            updateStatus(statusElementId, 'Connecting...', 'info');
            
            // Close existing connection
            if (currentSocket) {
                currentSocket.close();
                currentSocket = null;
            }

            try {
                // Add test parameters
                const testUrl = `${url}?url=${encodeURIComponent(window.location.href)}&userId=test_user_${Date.now()}&userName=Test%20User`;
                log(`Full URL: ${testUrl}`);
                
                const socket = new WebSocket(testUrl);
                
                socket.onopen = function(event) {
                    log(`✅ WebSocket connected to ${url}`);
                    updateStatus(statusElementId, `Connected to ${url}`, 'success');
                    updateStatus('message-status', 'Ready to send messages', 'success');
                    currentSocket = socket;
                };
                
                socket.onmessage = function(event) {
                    log(`📥 Received message: ${event.data}`);
                    try {
                        const data = JSON.parse(event.data);
                        log(`📋 Parsed message: ${JSON.stringify(data, null, 2)}`);
                    } catch (e) {
                        log(`📋 Raw message: ${event.data}`);
                    }
                };
                
                socket.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`);
                    updateStatus(statusElementId, `Connection error to ${url}`, 'error');
                    updateStatus('message-status', 'Connection failed', 'error');
                };
                
                socket.onclose = function(event) {
                    log(`🔌 WebSocket closed: Code ${event.code}, Reason: ${event.reason}`);
                    if (event.code !== 1000) {
                        updateStatus(statusElementId, `Connection closed unexpectedly (${event.code})`, 'error');
                    } else {
                        updateStatus(statusElementId, `Connection closed normally`, 'info');
                    }
                    updateStatus('message-status', 'Connection closed', 'info');
                    if (currentSocket === socket) {
                        currentSocket = null;
                    }
                };
                
                // Set timeout for connection
                setTimeout(() => {
                    if (socket.readyState === WebSocket.CONNECTING) {
                        log(`⏰ Connection timeout for ${url}`);
                        socket.close();
                        updateStatus(statusElementId, `Connection timeout to ${url}`, 'error');
                    }
                }, 10000);
                
            } catch (error) {
                log(`❌ Failed to create WebSocket connection: ${error.message}`);
                updateStatus(statusElementId, `Failed to connect to ${url}: ${error.message}`, 'error');
            }
        }

        function testProductionWebSocket() {
            testWebSocketConnection('wss://ws.tiptop.qubitrhythm.com', 'prod-status');
        }

        function testLocalWebSocket() {
            testWebSocketConnection('ws://localhost:8080', 'local-status');
        }

        function sendTestMessage() {
            const messageInput = document.getElementById('test-message');
            const message = messageInput.value.trim();
            
            if (!message) {
                updateStatus('message-status', 'Please enter a test message', 'error');
                return;
            }

            if (!currentSocket || currentSocket.readyState !== WebSocket.OPEN) {
                updateStatus('message-status', 'No active WebSocket connection', 'error');
                return;
            }

            try {
                const testMessage = {
                    type: 'chat',
                    content: message,
                    userId: `test_user_${Date.now()}`,
                    userName: 'Test User',
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                };

                log(`📤 Sending test message: ${JSON.stringify(testMessage)}`);
                currentSocket.send(JSON.stringify(testMessage));
                
                updateStatus('message-status', 'Test message sent!', 'success');
                messageInput.value = '';
                
            } catch (error) {
                log(`❌ Error sending message: ${error.message}`);
                updateStatus('message-status', `Error sending message: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('WebSocket test page loaded');
            log('Testing WebSocket connectivity...');
        });

        // Handle Enter key in message input
        document.getElementById('test-message').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendTestMessage();
            }
        });
    </script>
</body>
</html>
