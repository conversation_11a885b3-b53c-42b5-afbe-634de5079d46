# TipTop Chrome Extension: Critical WebSocket & State Issues

## Core Problems
1. **WebSocket Degradation**: Initial connection succeeds but fails later with "WebSocket not connected" errors
2. **State Persistence**: Chat history and user lists persist across page navigation

## Technical Context
- **Extension**: Chrome Manifest V3
- **Stack**: Background Service Worker → WebSocket Server (GCP Kubernetes) → PostgreSQL/Redis
- **Server**: `wss://ws.tiptop.qubitrhythm.com` in `tiptop` GCP namespace

## Problem Analysis & Requirements
**WebSocket Issues**:
- `backgroundSocket` becomes null/disconnected
- No automatic reconnection mechanism
- Initial connection succeeds but subsequent messages fail

**State Management Issues**:
- No URL change detection
- `activeUsers[]` and `messageQueue[]` persist across navigation
- Chat history doesn't clear on page changes

**Success Criteria**:
- Messages work throughout session
- State clears completely on navigation
- Auto-reconnecting WebSocket

## Key Files & Evidence
**Extension**:
- `background.js` (WebSocket handling)
- `social-client.js` (messaging)
- `content.js` (UI)
- `manifest.json`

**Server**:
- `websocket-server/server.js`
- Kubernetes configs (`k8s-tiptop/`)

**Log Evidence**:
- `WebSocket not connected` errors
- `Failed to send chat message through background`
- Initial success → disconnection → failure pattern
- Log files: `console_logs_errors.txt`, `console_logs.txt`, `console_logs_web.txt`

## Technical Focus Areas
1. WebSocket lifecycle management in service workers
2. Reliable URL change detection in Manifest V3
3. State cleanup patterns for Chrome extensions
4. Connection recovery mechanisms

**Goal**: Implement robust WebSocket communication with proper state management across navigation.
