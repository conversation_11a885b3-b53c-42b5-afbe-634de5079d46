#!/usr/bin/env node

const WebSocket = require('ws');

// Test WebSocket connection to TipTop server
const testUrl = 'https://example.com/test';
const userId = 'test-user-123';
const userName = 'Test User';

const wsUrl = `wss://ws.tiptop.qubitrhythm.com?url=${encodeURIComponent(testUrl)}&userId=${userId}&userName=${encodeURIComponent(userName)}`;

console.log('Testing WebSocket connection to:', wsUrl);

const ws = new WebSocket(wsUrl);

ws.on('open', () => {
  console.log('✅ WebSocket connection established successfully');
  
  // Send a test chat message
  const testMessage = {
    type: 'chat',
    content: 'Test message from Node.js client',
    userId: userId,
    userName: userName,
    url: testUrl,
    messageId: `test-${Date.now()}`,
    timestamp: new Date().toISOString()
  };
  
  console.log('📤 Sending test message:', testMessage);
  ws.send(JSON.stringify(testMessage));
  
  // Close connection after 5 seconds
  setTimeout(() => {
    console.log('🔌 Closing connection');
    ws.close();
  }, 5000);
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📥 Received message:', message.type, message);
  } catch (error) {
    console.log('📥 Received raw data:', data.toString());
  }
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error);
});

ws.on('close', (code, reason) => {
  console.log('🔌 WebSocket closed:', code, reason.toString());
  process.exit(0);
});

// Timeout after 10 seconds
setTimeout(() => {
  console.log('⏰ Test timeout');
  process.exit(1);
}, 10000);
